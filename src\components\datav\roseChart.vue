<template>
  <div id="rose-chart">
    <div class="rose-chart-title">学生请假情况</div>
    <dv-charts :option="option" />
  </div>
</template>

<script>
export default {
  name: '<PERSON><PERSON><PERSON>',
  data () {
    return {
      option: {}
    }
  },
  methods: {
    createData () {
      this.option = {
        series: [
          {
            type: 'histogram',
            data: [
              { name: '电子信息工程学院', value: 15 },
              { name: '机械制造工程学院', value: 12 },
              { name: '云南化工学院', value: 8 },
              { name: '商务管理学院', value: 6 },
              { name: '生物工程学院', value: 4 }
            ],
            barStyle: {
              fill: '#1e88e5'
            }
          }
        ],
        xAxis: {
          name: '学院',
          data: ['电子信息工程学院', '机械制造工程学院', '云南化工学院', '商务管理学院', '生物工程学院'],
          nameTextStyle: {
            fill: '#fff',
            fontSize: 14
          },
          axisLabel: {
            fill: '#fff',
            fontSize: 11
          },
          axisLine: {
            style: {
              stroke: '#03d3ec'
            }
          }
        },
        yAxis: {
          name: '请假人数',
          nameTextStyle: {
            fill: '#fff',
            fontSize: 14
          },
          axisLabel: {
            fill: '#fff',
            fontSize: 12
          },
          axisLine: {
            style: {
              stroke: '#03d3ec'
            }
          },
          splitLine: {
            style: {
              stroke: 'rgba(255, 255, 255, 0.1)'
            }
          }
        }
      }
    }
  },
  mounted () {
    const { createData } = this

    createData()

    setInterval(createData, 30000)
  }
}
</script>

<style lang="less">
#rose-chart {
  width: 100%;
  height: 100%;
  background-color: rgba(6, 30, 93, 0.5);
  border-top: 2px solid rgba(1, 153, 209, .5);
  border-radius: 15px;
  box-sizing: border-box;

  .rose-chart-title {
    height: 50px;
    font-weight: bold;
    text-indent: 20px;
    font-size: 18px;
    display: flex;
    align-items: center;
    color: #ffffff;
    font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  }

  .dv-charts-container {
    height: calc(~"100% - 50px");
  }
}
</style>
